// Sidebar toggle functionality
const sidebar = document.getElementById('sidebar');
const sidebarToggle = document.getElementById('sidebarToggle');
const menuItems = document.querySelectorAll('.menu-item');

function toggleSidebar() {
    if (window.innerWidth <= 768) {
        sidebar.classList.toggle('open');
    } else {
        sidebar.classList.toggle('closed');
    }
}

sidebarToggle.addEventListener('click', toggleSidebar);

// Highlight selected menu item
menuItems.forEach(item => {
    item.addEventListener('click', function(e) {
        menuItems.forEach(i => i.classList.remove('active'));
        this.classList.add('active');
    });
});

// Responsive sidebar behavior
window.addEventListener('resize', () => {
    if (window.innerWidth > 768) {
        sidebar.classList.remove('open');
    }
});

// Optional: Close sidebar on outside click (mobile)
document.addEventListener('click', function(e) {
    if (window.innerWidth <= 768 && sidebar.classList.contains('open')) {
        if (!sidebar.contains(e.target) && e.target !== sidebarToggle) {
            sidebar.classList.remove('open');
        }
    }
});
