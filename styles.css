/* Sidebar Styles */
body {
    margin: 0;
    font-family: 'Inter', Arial, sans-serif;
    background: #f5f6fa;
}
.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    width: 270px;
    background: #fff;
    box-shadow: 2px 0 8px rgba(0,0,0,0.04);
    display: flex;
    flex-direction: column;
    transition: width 0.3s cubic-bezier(.4,0,.2,1);
    z-index: 100;
}
.sidebar.closed {
    width: 64px;
}
.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 20px 12px 20px;
}
.logo {
    font-weight: 600;
    font-size: 1.2rem;
    color: #5c5cfa;
    display: flex;
    align-items: center;
    gap: 8px;
}
.logo-img {
    width: 32px;
    height: 32px;
    border-radius: 8px;
}
.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #aaa;
    cursor: pointer;
    transition: color 0.2s;
}
.sidebar-toggle:hover {
    color: #5c5cfa;
}
.sidebar-search {
    padding: 0 20px 16px 20px;
}
.sidebar-search input {
    width: 100%;
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    font-size: 1rem;
    background: #f7f7fb;
    outline: none;
    transition: border-color 0.2s;
}
.sidebar-search input:focus {
    border-color: #5c5cfa;
}
.sidebar-menu ul,
.sidebar-footer ul {
    list-style: none;
    margin: 0;
    padding: 0;
}
.sidebar-menu ul {
    margin-bottom: 24px;
}
.menu-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px 20px;
    color: #444;
    text-decoration: none;
    font-size: 1rem;
    border-radius: 8px;
    transition: background 0.2s, color 0.2s;
}
.menu-item .icon {
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
}
.menu-item:hover,
.menu-item.active {
    background: #f0f1ff;
    color: #5c5cfa;
}
.sidebar-footer {
    margin-top: auto;
    padding-bottom: 16px;
}
.sidebar-user {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px 0 20px;
}
.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #eee;
}
.user-info {
    display: flex;
    flex-direction: column;
}
.user-name {
    font-weight: 500;
    font-size: 1rem;
}
.user-email {
    font-size: 0.85rem;
    color: #888;
}
.main-content {
    margin-left: 270px;
    padding: 32px;
    transition: margin-left 0.3s cubic-bezier(.4,0,.2,1);
}
.sidebar.closed ~ .main-content {
    margin-left: 64px;
}
@media (max-width: 768px) {
    .sidebar {
        position: absolute;
        height: 100%;
        width: 220px;
        left: -220px;
        transition: left 0.3s cubic-bezier(.4,0,.2,1);
    }
    .sidebar.open {
        left: 0;
    }
    .main-content {
        margin-left: 0;
        padding: 16px;
    }
}
